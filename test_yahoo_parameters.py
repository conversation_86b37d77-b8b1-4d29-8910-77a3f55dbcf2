#!/usr/bin/env python3
"""
Yahoo Shopping API パラメータテスト

このスクリプトは、Yahoo Shopping APIの全パラメータを使用した検索例を示します。
"""

from app.services.adapters.yahoo import YahooAdapter


def test_all_parameters():
    """全パラメータを使用したテスト例"""
    adapter = YahooAdapter()
    
    # 基本的な検索例
    print("=== 基本検索 ===")
    results = adapter.search_products(
        query="iPhone",
        results=10,
        start=1,
        sort="+price"  # 価格の安い順
    )
    print(f"基本検索結果: {len(results)}件")
    
    # 詳細パラメータを使用した検索例
    print("\n=== 詳細パラメータ検索 ===")
    results = adapter.search_products(
        query="スマートフォン",
        results=20,
        start=1,
        price_from=10000,
        price_to=100000,
        sort="-review_count",  # レビュー数の多い順
        in_stock=True,  # 在庫ありのみ
        image_size=300,  # 300x300の画像
        condition="new",  # 新品のみ
        shipping="free",  # 送料無料
        user_rank="gold",  # ゴールド会員
        is_discounted=True,  # セール商品のみ
        affiliate_rate_from=5.0,  # アフィリエイト料率5%以上
    )
    print(f"詳細検索結果: {len(results)}件")
    
    # JANコード検索例
    print("\n=== JANコード検索 ===")
    results = adapter.search_products(
        jan_code="4905524535815",
        results=5
    )
    print(f"JANコード検索結果: {len(results)}件")
    
    # カテゴリ・ブランド検索例
    print("\n=== カテゴリ・ブランド検索 ===")
    results = adapter.search_products(
        query="時計",
        genre_category_id="2495,2496",  # 複数カテゴリ指定
        brand_id="149,150",  # 複数ブランド指定
        results=15
    )
    print(f"カテゴリ・ブランド検索結果: {len(results)}件")
    
    # 配送オプション検索例
    print("\n=== 配送オプション検索 ===")
    results = adapter.search_products(
        query="ギフト",
        delivery_area="13",  # 東京都
        delivery_day=1,  # あすつく
        delivery_deadline=15,  # 15時締切
        results=10
    )
    print(f"配送オプション検索結果: {len(results)}件")
    
    # 販売期間指定検索例
    print("\n=== 販売期間指定検索 ===")
    import time
    current_time = int(time.time())
    results = adapter.search_products(
        query="限定商品",
        sale_start_from=current_time - 86400,  # 1日前から
        sale_end_to=current_time + 86400 * 7,  # 1週間後まで
        preorder=True,  # 予約商品のみ
        results=10
    )
    print(f"販売期間指定検索結果: {len(results)}件")


def test_parameter_validation():
    """パラメータバリデーションのテスト"""
    adapter = YahooAdapter()
    
    print("\n=== パラメータバリデーションテスト ===")
    
    # 無効な画像サイズ
    try:
        adapter.search_products(query="test", image_size=200)
        print("ERROR: 無効な画像サイズが受け入れられました")
    except ValueError as e:
        print(f"✓ 画像サイズバリデーション: {e}")
    
    # 無効な配送日
    try:
        adapter.search_products(query="test", delivery_day=5)
        print("ERROR: 無効な配送日が受け入れられました")
    except ValueError as e:
        print(f"✓ 配送日バリデーション: {e}")
    
    # 無効なユーザーランク
    try:
        adapter.search_products(query="test", user_rank="invalid")
        print("ERROR: 無効なユーザーランクが受け入れられました")
    except ValueError as e:
        print(f"✓ ユーザーランクバリデーション: {e}")
    
    # 無効なソート順
    try:
        adapter.search_products(query="test", sort="invalid")
        print("ERROR: 無効なソート順が受け入れられました")
    except ValueError as e:
        print(f"✓ ソート順バリデーション: {e}")


def show_parameter_examples():
    """パラメータの使用例を表示"""
    print("\n=== パラメータ使用例 ===")
    
    examples = {
        "基本検索": {
            "query": "iPhone",
            "results": 20,
            "sort": "+price"
        },
        "価格範囲指定": {
            "query": "ノートパソコン",
            "price_from": 50000,
            "price_to": 150000
        },
        "アフィリエイト料率指定": {
            "query": "化粧品",
            "affiliate_rate_from": 10.0,
            "affiliate_type": "vc",
            "affiliate_id": "your_affiliate_id"
        },
        "配送オプション": {
            "query": "プレゼント",
            "shipping": "free",
            "delivery_area": "13",  # 東京都
            "delivery_day": 1,  # あすつく
            "delivery_deadline": 13  # 13時締切
        },
        "商品属性": {
            "query": "スマートフォン",
            "condition": "new",
            "in_stock": True,
            "is_discounted": True,
            "preorder": False
        }
    }
    
    for name, params in examples.items():
        print(f"\n{name}:")
        for key, value in params.items():
            print(f"  {key}: {value}")


if __name__ == "__main__":
    print("Yahoo Shopping API 全パラメータテスト")
    print("=" * 50)
    
    # パラメータ使用例を表示
    show_parameter_examples()
    
    # バリデーションテスト
    test_parameter_validation()
    
    # 実際のAPI呼び出しテスト（API キーが設定されている場合のみ）
    print("\n注意: 実際のAPI呼び出しを行うには、Yahoo API キーが必要です。")
    print("API キーが設定されていない場合は、モックデータが返されます。")
    
    # test_all_parameters()  # コメントアウト（必要に応じて有効化）
