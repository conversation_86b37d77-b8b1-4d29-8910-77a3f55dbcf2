from typing import List, Dict, Any, Optional
import requests
import os
from app.services.adapters.mock_provider import MockMarketAdapter
from app.data_access.repositories import get_provider_config


class YahooAdapter(MockMarketAdapter):
    """
    Yahoo!ショッピングAPIアダプター
    API Documentation: https://developer.yahoo.co.jp/webapi/shopping/v3/itemsearch.html

    対応API:
    - 商品検索API (v3)
    """

    def __init__(self):
        """
        認証情報を設定から取得
        """
        config = get_provider_config("yahoo")
        if config:
            self.app_id = config.get("api_key")  # Yahoo! Client ID (アプリケーションID)
        else:
            # 環境変数からフォールバック
            self.app_id = os.environ.get("YAHOO_API_KEY")

        self.base_url = "https://shopping.yahooapis.jp/ShoppingWebService/V3/itemSearch"

    def _make_request(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        Yahoo!ショッピングAPIへのリクエストを実行
        """
        if not self.app_id:
            print("Yahoo API key not configured, falling back to mock data")
            return {"error": "API key not configured"}

        # 必須パラメータを追加
        request_params = {"appid": self.app_id, **params}

        print(f"Making request to Yahoo Shopping API")
        print(f"Parameters: {request_params}")

        try:
            response = requests.get(
                self.base_url,
                params=request_params,
                timeout=30,
                headers={"User-Agent": "EC-SaaS/1.0"},
            )

            print(f"Response status: {response.status_code}")
            response.raise_for_status()

            result = response.json()
            print(
                f"Response keys: {list(result.keys()) if isinstance(result, dict) else type(result)}"
            )
            return result

        except requests.exceptions.RequestException as e:
            print(f"Yahoo Shopping API request error: {e}")
            return {"error": str(e)}
        except Exception as e:
            print(f"Yahoo Shopping API error: {e}")
            return {"error": str(e)}

    def _parse_yahoo_items(
        self, api_response: Dict[str, Any], query: str
    ) -> List[Dict[str, Any]]:
        """
        Yahoo!ショッピングAPIのレスポンスを標準形式に変換
        """
        if "error" in api_response:
            print(f"API error in response: {api_response['error']}")
            return []

        hits = api_response.get("hits", [])
        if not hits:
            print("No hits found in Yahoo Shopping API response")
            return []

        results = []
        for i, item in enumerate(hits):
            try:
                # 価格情報の取得
                price = item.get("price", 0)

                # 画像URLの取得
                image_url = ""
                if item.get("image"):
                    image_url = item["image"].get(
                        "medium", item["image"].get("small", "")
                    )
                elif item.get("exImage"):
                    image_url = item["exImage"].get("url", "")

                # レビュー情報の取得
                review_info = item.get("review", {})
                review_count = review_info.get("count", 0)
                review_rate = review_info.get("rate", 0.0)

                # ブランド情報の取得
                brand_info = item.get("brand", {})
                brand_name = brand_info.get("name", "")

                # ストア情報の取得
                seller_info = item.get("seller", {})
                shop_name = seller_info.get("name", "")
                shop_id = seller_info.get("sellerId", "")

                # ジャンル情報の取得
                genre_info = item.get("genreCategory", {})
                genre_name = genre_info.get("name", "")

                results.append(
                    {
                        "listing_id": item.get("code", f"YAHOO-{i}"),
                        "sku": item.get("code", f"SKU-{i}"),
                        "title": item.get("name", f"{query} - Yahoo商品{i}"),
                        "price": price,
                        "stock": (
                            100 if item.get("inStock", True) else 0
                        ),  # Yahoo APIでは在庫数は取得できない
                        "reviews": review_count,
                        "rating": review_rate,
                        "url": item.get("url", ""),
                        "shop_name": shop_name,
                        "shop_id": shop_id,
                        "brand_name": brand_name,
                        "genre_name": genre_name,
                        "image_url": image_url,
                        "description": item.get("description", ""),
                        "headline": item.get("headLine", ""),
                        "condition": item.get("condition", "new"),
                        "jan_code": item.get("janCode", ""),
                        "affiliate_rate": item.get("affiliateRate", 0.0),
                        "premium_price": item.get("premiumPrice", price),
                        "is_best_seller": seller_info.get("isBestSeller", False),
                        "shipping_code": item.get("shipping", {}).get("code", 1),
                        "payment_methods": item.get("payment", ""),
                    }
                )

            except Exception as e:
                print(f"Error parsing Yahoo item {i}: {e}")
                continue

        print(f"Successfully parsed {len(results)} items from Yahoo Shopping API")
        return results

    def search_products(
        self,
        affiliate_type: Optional[str] = None,
        affiliate_id: Optional[str] = None,
        query: Optional[str] = None,
        jan_code: Optional[str] = None,
        image_size: Optional[int] = None,
        genre_category_id: Optional[str] = None,
        brand_id: Optional[str] = None,
        seller_id: Optional[str] = None,
        price_from: Optional[int] = None,
        price_to: Optional[int] = None,
        affiliate_rate_from: Optional[float] = None,
        affiliate_rate_to: Optional[float] = None,
        preorder: Optional[bool] = None,
        results: int = 20,
        start: int = 1,
        in_stock: Optional[bool] = None,
        is_discounted: Optional[bool] = None,
        shipping: Optional[str] = None,
        payment: Optional[str] = None,
        user_rank: str = "guest",
        sale_end_from: Optional[int] = None,
        sale_end_to: Optional[int] = None,
        sale_start_from: Optional[int] = None,
        sale_start_to: Optional[int] = None,
        delivery_area: Optional[str] = None,
        delivery_day: Optional[int] = None,
        delivery_deadline: Optional[int] = None,
        sort: str = "-score",
        condition: Optional[str] = None,
        **extra_params: Any,
    ) -> List[Dict[str, Any]]:
        """
        Yahoo!ショッピング商品検索

        Args:
            query: 検索キーワード
            results: 検索結果の返却数 (1-100, デフォルト: 20)
            start: 返却結果の先頭位置 (デフォルト: 1)
            price_from: 商品価格 (下限) (下限は含む)
            price_to: 商品価格 (上限) (上限は含む)
            sort: 並び順を指定 (-score: おすすめ順, +price: 価格の安い順, -price: 価格の高い順, -review_count: 商品レビュー数の多い順)
            in_stock: 在庫有無 (True: 在庫ありのみ, False: 在庫なしのみ, None: 指定なし)
            affiliate_type: バリューコマースアフィリエイト（vc）を選択
            affiliate_id: バリューコマースアフィリエイトID
            jan_code: JANコード
            image_size: 取得したい任意の画像サイズ (76, 106, 132, 146, 300, 600)
            genre_category_id: ジャンルカテゴリID (カンマ区切りで複数指定可能、OR絞り込み)
            brand_id: ブランドID (カンマ区切りで複数指定可能、OR絞り込み)
            seller_id: ストアID
            affiliate_rate_from: アフィリエイト料率(下限)（下限は含む）
            affiliate_rate_to: アフィリエイト料率(上限)（上限は含む）
            preorder: 予約商品の指定 (True: 予約商品のみ)
            is_discounted: セール対象商品に絞り込み
            shipping: 送料区分の指定 (free: 送料無料, conditional_free: 条件付き送料無料)
            payment: 支払い方法 (1: クレジットカード, 2: 銀行振込, 4: 商品代引, etc.)
            user_rank: 会員属性 (diamond/platinum/gold/silver/bronze/guest)
            sale_end_from: 販売終了UNIX時間の下限 ()
            sale_end_to: 販売終了UNIX時間の上限
            sale_start_from: 販売開始UNIX時間の下限
            sale_start_to: 販売開始UNIX時間の上限
            delivery_area: きょうつく、あすつく、翌々日配送の都道府県の指定 (JIS都道府県コード)
            delivery_day: あすつく、きょうつく、翌々日配送の指定 (0: きょうつく, 1: あすつく, 2: 翌々日配送)
            delivery_deadline: 発送日の締め時間を指定 (1-24時間表記、99: 現在時+1時間)
            condition: 商品状態の指定 (used: 中古, new: 新品)
            **extra_params: その他のAPIパラメータ
        """
        if not self.app_id:
            print("Yahoo API key not configured, using mock data")
            return super().search_products(query or "")

        # Validate parameters
        if results + start > 1000:
            raise ValueError("results+start must be less than 1000")
        if image_size is not None and image_size not in [76, 106, 132, 146, 300, 600]:
            raise ValueError("image_size must be one of: 76, 106, 132, 146, 300, 600")

        if delivery_day is not None and delivery_day not in [0, 1, 2]:
            raise ValueError(
                "delivery_day must be one of: 0 (きょうつく), 1 (あすつく), 2 (翌々日配送)"
            )

        if delivery_deadline is not None and not (
            1 <= delivery_deadline <= 24 or delivery_deadline == 99
        ):
            raise ValueError("delivery_deadline must be between 1-24 or 99")

        if user_rank not in [
            "diamond",
            "platinum",
            "gold",
            "silver",
            "bronze",
            "guest",
        ]:
            raise ValueError(
                "user_rank must be one of: diamond, platinum, gold, silver, bronze, guest"
            )

        if condition is not None and condition not in ["used", "new"]:
            raise ValueError("condition must be 'used' or 'new'")

        if sort not in ["-score", "+price", "-price", "-review_count"]:
            raise ValueError(
                "sort must be one of: -score, +price, -price, -review_count"
            )

        params: Dict[str, Any] = {
            "results": min(max(results, 1), 100),  # 1-100の範囲に制限
            "start": max(start, 1),
            "sort": sort,
            "user_rank": user_rank,
        }

        # 検索キーワードまたはJANコードが必要
        if query is not None:
            params["query"] = query
        if jan_code is not None:
            params["jan_code"] = jan_code

        # 価格関連パラメータ
        if price_from is not None:
            params["price_from"] = price_from
        if price_to is not None:
            params["price_to"] = price_to

        # アフィリエイト関連パラメータ
        if affiliate_type is not None:
            params["affiliate_type"] = affiliate_type
        if affiliate_id is not None:
            params["affiliate_id"] = affiliate_id
        if affiliate_rate_from is not None:
            params["affiliate_rate_from"] = affiliate_rate_from
        if affiliate_rate_to is not None:
            params["affiliate_rate_to"] = affiliate_rate_to

        # 商品属性パラメータ
        if image_size is not None:
            params["image_size"] = image_size
        if genre_category_id is not None:
            params["genre_category_id"] = genre_category_id
        if brand_id is not None:
            params["brand_id"] = brand_id
        if seller_id is not None:
            params["seller_id"] = seller_id
        if condition is not None:
            params["condition"] = condition

        # 在庫・販売状態パラメータ
        if in_stock is not None:
            params["in_stock"] = in_stock
        if preorder is not None:
            params["preorder"] = preorder
        if is_discounted is not None:
            params["is_discounted"] = is_discounted

        # 配送・支払いパラメータ
        if shipping is not None:
            params["shipping"] = shipping
        if payment is not None:
            params["payment"] = payment

        # 販売期間パラメータ
        if sale_end_from is not None:
            params["sale_end_from"] = sale_end_from
        if sale_end_to is not None:
            params["sale_end_to"] = sale_end_to
        if sale_start_from is not None:
            params["sale_start_from"] = sale_start_from
        if sale_start_to is not None:
            params["sale_start_to"] = sale_start_to

        # 配送オプションパラメータ（3つ全て指定された場合のみ有効）
        if delivery_area is not None:
            params["delivery_area"] = delivery_area
        if delivery_day is not None:
            params["delivery_day"] = delivery_day
        if delivery_deadline is not None:
            params["delivery_deadline"] = delivery_deadline

        # 追加パラメータ
        for key, value in extra_params.items():
            if value is not None:
                params[key] = value

        api_response = self._make_request(params)
        return self._parse_yahoo_items(api_response, query or "")
